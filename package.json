{"name": "boostcast-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"lucide-react": "^0.525.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-ga4": "^2.1.0", "react-gtm-module": "^2.0.11", "react-hotjar": "^6.3.1", "react-microsoft-clarity": "^2.0.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-gtm-module": "^2.0.4", "tailwindcss": "^4", "typescript": "^5"}}