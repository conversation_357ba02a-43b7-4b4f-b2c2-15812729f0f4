"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import { MenuProps } from "./menu.types";
import { MenuIcon, XIcon } from "lucide-react";
import { useSmartScrollToHash } from "@/app/hooks/useSmartScrollToHash";

export default function Menu({ items }: MenuProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [isScrolled, setIsScrolled] = useState(false);

    const handleSmartScroll = useSmartScrollToHash();

    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 30);
        };

        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, []);

    return (
        <header
            className={`fixed top-0 left-0 right-0 z-50 transition-colors duration-300 px-6 py-4 border-b border-gray-800 backdrop-blur ${isScrolled ? "bg-black/70" : "bg-black"
                } flex justify-between items-center`}
        >
            <button
                onClick={() => handleSmartScroll('/')}
                className="flex items-center gap-2 text-xl font-bold text-white-400 focus:outline-none"
            >
                <Image
                    src="/images/logo-mark.svg"
                    alt="Boostcast Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                />
                    boostcast
            </button>

            {/* Desktop Nav */}
            <nav className="hidden md:flex gap-6 text-sm">
                {items.map((item, index) => (
                    <a
                        key={index}
                        href={item.href}
                        className={`hover:text-purple-400 ${item.className || ""}`}
                        onClick={(e) => {
                            e.preventDefault();
                            handleSmartScroll(item.href);
                        }}
                    >
                        {item.label}
                    </a>
                ))}
            </nav>

            <div className="hidden md:block">
                <a
                    href="https://create.boostcast.pro"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-purple-600 text-white text-sm font-semibold px-4 py-2 rounded hover:bg-purple-700 flex flex-col items-center leading-tight"
                >
                    <span>Start FREE Trial</span>
                    <span className="text-xs opacity-90">No credit card required</span>
                </a>
            </div>

            {/* Mobile Burger */}
            <div className="md:hidden relative">
                <button onClick={() => setIsOpen(!isOpen)} aria-label="Toggle menu">
                    {isOpen ? <XIcon size={24} /> : <MenuIcon size={24} />}
                </button>

                {isOpen && (
                    <div className="absolute top-full right-0 mt-2 w-48 bg-black border border-zinc-700 rounded shadow-lg z-50">
                        <div className="flex flex-col px-4 py-3 text-sm">
                            {items.map((item, index) => (
                                <a
                                    key={index}
                                    href={item.href}
                                    className="py-2 text-white hover:text-purple-400"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        handleSmartScroll(item.href);
                                        setIsOpen(false)
                                    }}
                                >
                                    {item.label}
                                </a>
                            ))}
                            <a
                                href="https://create.boostcast.pro"
                                target="_blank"
                                rel="noopener noreferrer"
                                onClick={() => setIsOpen(false)}
                                className="mt-3 bg-purple-600 text-white text-sm font-semibold px-4 py-2 rounded hover:bg-purple-700 flex flex-col items-center leading-tight"
                            >
                                <span>Start FREE Trial</span>
                                <span className="text-xs opacity-90">No credit card required</span>
                            </a>
                        </div>
                    </div>
                )}
            </div>
        </header>
    );
}