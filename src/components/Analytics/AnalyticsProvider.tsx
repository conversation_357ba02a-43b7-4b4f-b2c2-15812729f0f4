'use client';

import { useEffect } from 'react';
import { initializeAnalytics, isAnalyticsEnabled } from '@/utils/analytics';
import { debugAnalytics } from '@/utils/debug-analytics';

interface AnalyticsProviderProps {
  children: React.ReactNode;
}

export default function AnalyticsProvider({ children }: AnalyticsProviderProps) {
  useEffect(() => {
    console.log('AnalyticsProvider useEffect triggered');
    console.log('isAnalyticsEnabled():', isAnalyticsEnabled());

    // Run debug info in development
    if (process.env.NODE_ENV === 'development') {
      debugAnalytics();
    }

    // Initialize analytics on mount (client-side only)
    if (isAnalyticsEnabled()) {
      console.log('Analytics enabled, initializing...');
      initializeAnalytics();
    } else {
      console.log('Analytics not enabled - no tracking IDs found');
    }
  }, []);

  return <>{children}</>;
}
