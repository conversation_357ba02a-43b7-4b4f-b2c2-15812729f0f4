'use client';

import { useEffect } from 'react';
import { initializeAnalytics, isAnalyticsEnabled } from '@/utils/analytics';

interface AnalyticsProviderProps {
  children: React.ReactNode;
}

export default function AnalyticsProvider({ children }: AnalyticsProviderProps) {
  useEffect(() => {
    // Initialize analytics on mount (client-side only)
    if (isAnalyticsEnabled()) {
      initializeAnalytics();
    }
  }, []);

  return <>{children}</>;
}
