export default function PricingCard({
    title,
    price,
    period,
    currency = "$",
    features,
    cta,
    ctaUrl,
    subtext,
    highlighted = false,
}: PricingCardProps) {
    return (
        <div className={`relative z-10 flex flex-col bg-zinc-900 p-6 rounded border border-zinc-700 w-full md:w-80 ${highlighted ? 'shadow-lg ring-1 ring-purple-600' : ''}`}>
            <h3 className="text-xl font-bold mb-2">{title}</h3>

            <div className="h-16 mb-6 flex items-end">
                {typeof price === "number" ? (
                    <div className="text-left">
                        <span className="text-4xl font-bold text-white">{currency}{price}</span>
                        <span className="text-sm text-gray-400"> /{period}</span>
                    </div>
                ) : (
                    <div className="text-left">
                        <span className="text-3xl font-bold text-white">{price}</span>
                    </div>
                )}
            </div>

            <ul className="text-sm text-gray-300 mb-6 space-y-1 text-left">
                {features.map((f, i) => <li key={i}>{f}</li>)}
            </ul>

            <div className="mt-auto">
                {ctaUrl ? (
                    <a
                        href={ctaUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 text-sm font-semibold w-full block text-center"
                    >
                        {cta}
                    </a>
                ) : (
                    <button className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 text-sm font-semibold w-full">
                        {cta}
                    </button>
                )}
                {subtext && <p className="text-xs text-gray-400 mt-2 text-center">{subtext}</p>}
            </div>

            {highlighted && (
                <div className="absolute -z-10 top-1/2 left-1/2 w-64 h-64 bg-purple-600 rounded-full opacity-20 transform -translate-x-1/2 -translate-y-1/2"></div>
            )}
        </div>
    );
}