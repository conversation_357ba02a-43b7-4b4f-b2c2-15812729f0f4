import { Twitter, Instagram, Mail } from 'lucide-react';

export default function Footer() {
    return (
        <footer className="bg-black text-gray-400 px-6 py-8 border-t border-gray-800">
            <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center gap-6">

                {/* Left: Links */}
                <div className="flex flex-col md:flex-row items-center gap-6 text-sm font-semibold uppercase">
                    <a href="/privacy-policy" className="hover:text-white">Privacy Policy</a>
                    <a href="/terms" className="hover:text-white">Terms and Conditions</a>
                </div>

                {/* Right: Social Icons */}
                <div className="flex items-center gap-5 text-gray-400">
                    <a href="https://twitter.com/" aria-label="Twitter" className="hover:text-white">
                        <Twitter size={20} />
                    </a>
                    <a href="https://instagram.com/" aria-label="Instagram" className="hover:text-white">
                        <Instagram size={20} />
                    </a>
                    <a
                        href={process.env.NEXT_PUBLIC_DISCORD_URL}
                        aria-label="Discord"
                        className="hover:text-white"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                            width="20"
                            height="20"
                        >
                            <path d="M20.317 4.369a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.249a18.27 18.27 0 0 0-5.487 0 12.69 12.69 0 0 0-.617-1.249.077.077 0 0 0-.079-.037c-1.67.292-3.28.808-4.884 1.515a.07.07 0 0 0-.032.027C.533 9.034-.32 13.579.099 18.057a.081.081 0 0 0 .031.056 19.962 19.962 0 0 0 5.993 3.028.078.078 0 0 0 .084-.027c.461-.63.873-1.295 1.226-1.994a.076.076 0 0 0-.041-.105c-.652-.247-1.27-.556-1.868-.923a.078.078 0 0 1-.007-.127c.125-.094.25-.191.372-.287a.074.074 0 0 1 .077-.01c3.927 1.786 8.18 1.786 12.061 0a.074.074 0 0 1 .078.009c.123.096.247.193.372.288a.078.078 0 0 1-.006.126 12.296 12.296 0 0 1-1.869.922.076.076 0 0 0-.04.106c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.933 19.933 0 0 0 6.002-3.028.077.077 0 0 0 .03-.055c.5-5.177-.838-9.679-3.548-13.662a.062.062 0 0 0-.03-.028zM8.02 15.331c-1.183 0-2.155-1.085-2.155-2.419 0-1.333.955-2.418 2.155-2.418 1.213 0 2.175 1.096 2.155 2.418 0 1.334-.955 2.419-2.155 2.419zm7.974 0c-1.183 0-2.155-1.085-2.155-2.419 0-1.333.955-2.418 2.155-2.418 1.213 0 2.175 1.096 2.155 2.418 0 1.334-.942 2.419-2.155 2.419z" />
                        </svg>
                    </a>
                    <a href="mailto:<EMAIL>" aria-label="Email" className="hover:text-white">
                        <Mail size={20} />
                    </a>
                </div>
            </div>

            <div className="mt-6 text-center text-sm text-gray-500 text-left">
                Copyright © 2025 Boostcast LLC. All rights reserved.
            </div>
        </footer>

    );
}