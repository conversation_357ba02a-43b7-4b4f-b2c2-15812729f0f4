import Image from "next/image";


export default function TestimonialCard({ image, name, title, quote }: TestimonialCardProps) {
    return (
        <div className="bg-zinc-800 p-4 rounded border border-zinc-700 flex flex-col gap-4">
            <p className="text-sm text-white">"{quote}"</p>
            <div className="flex items-center gap-3 mt-auto">
                <Image
                    src={image}
                    alt={name}
                    width={60}
                    height={60}
                    className="rounded-full object-cover"
                    style={{ objectPosition: "top" }}
                />
                <div>
                    <div className="font-semibold text-white">{name}</div>
                    <div className="text-xs text-gray-400">{title}</div>
                </div>
            </div>
        </div>
    );
}