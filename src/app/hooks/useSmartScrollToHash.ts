"use client";

import { usePathname, useRouter } from "next/navigation";

export function useSmartScrollToHash() {
  const pathname = usePathname();
  const router = useRouter();

  const scrollToSection = (id: string) => {
    const section = document.getElementById(id);
    if (section) {
      section.scrollIntoView({ behavior: "smooth" });

      // Push new hash to the URL without reloading
      const newUrl = `${window.location.pathname}#${id}`;
      window.history.pushState(null, "", newUrl);
    }
  };

  const handleClick = (hash: string) => {
    const id = hash.replace("#", "");

    if (pathname !== "/") {
      sessionStorage.setItem("scrollTarget", id);
      router.push("/");
    } else {
      scrollToSection(id);
    }
  };

  // On homepage, scroll to stored section
  if (typeof window !== "undefined") {
    const targetId = sessionStorage.getItem("scrollTarget");
    if (targetId && pathname === "/") {
      sessionStorage.removeItem("scrollTarget");
      setTimeout(() => scrollToSection(targetId), 80);
    }
  }

  return handleClick;
}
