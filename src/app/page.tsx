"use client"
import Image from "next/image";
import FeatureCard from "@/components/FeatureCard";
import PricingCard from "@/components/PricingCard";
import TestimonialCard from "@/components/TestimonialCard";
import {
  LucideSubtitles,
  LucideCrop,
  LucideListVideo,
  LucideCloud,
  LucideBot,
  LucideBrainCircuit,
  LucideFileVideo2
} from "lucide-react";
import { MenuItemsProps } from "@/components/Menu/menu.types";
import { Links } from "@/utils/types";
import { stripHash } from "@/utils/common";


export default function HomePage() {

  const cards: TestimonialCardProps[] = [
    {
      image: "/images/Eri-profile-pic-150x150.jpg",
      name: "<PERSON><PERSON><PERSON>",
      title: "Founder @WolfMediaDigital",
      quote: "Honestly, we used to have two people working full-time just making clips. Now we just use Boostcast and they’re doing other stuff. Saved us a ton of money."
    },
    {
      image: "/images/krisprofilepic-150x150.jpg",
      name: "<PERSON> <PERSON>",
      title: "<PERSON>Fi educator @DeFiDonut",
      quote: "I’m not great with editing software, so this has been a game-changer. Just upload and it does most of the work for me."
    },
    {
      image: "/images/timi-profile-pic-150x150.jpg",
      name: "BalkanBeast",
      title: "Founder @10ot10podcast",
      quote: "Love how it finds the good parts of my 2-hour podcasts automatically. Beats the hell out of listening to the whole thing again trying to remember the best bits."
    },
    {
      image: "/images/emo-profile-pic-150x150.jpg",
      name: "Emil",
      title: "Founder @SenecaDigital",
      quote: "Their support team actually responds when you message them. Had an issue with a file and they fixed it same day."
    }
  ];

  const prices: PricingCardProps[] = [
    {
      title: "Podcaster",
      price: 19,
      period: "month",
      features: [
        "AI Powered video editing",
        "180 Boost(Total of 3 hours video upload, 1 Boost = 1 minute of video upload)",
        "Additional Boost at $0.15 each",
        "Multi-language AI captions",
        "Vertical AI reframing",
        "30GB of storage"
      ],
      cta: "Start FREE trial",
      ctaUrl: "https://create.boostcast.pro",
      subtext: "Perfect for individual podcasters.",
      highlighted: false
    },
    {
      title: "Studio",
      price: 37,
      period: "month",
      features: [
        "Everything in Podcaster plus:",
        "500 Boost (Total of 8h20min video upload, 3x more than Podcaster)",
        "Additional Boost at $0.10 each (33% off)",
        "200GB of storage",
        "Early access to new features",
        "Priority processing"
      ],
      cta: "Subscribe",
      ctaUrl: "https://create.boostcast.pro",
      subtext: "Ideal for podcast marketing agencies",
      highlighted: true
    },
    {
      title: "Enterprise",
      price: "Let's Talk",
      period: "",
      features: [
        "Custom boost packages",
        "Pay as you go / End of month billing",
        "Team collaboration tools",
        "API access",
        "Custom branding options(custom sub domain / logo / colours)",
        "Priority support",
      ],
      cta: "Contact us",
      ctaUrl: "mailto:<EMAIL>",
      subtext: "Custom solutions for agencies and larger teams.",
      highlighted: false
    }
  ];

  const features: FeatureCardProps[] = [
     {
      icon: <LucideBrainCircuit size={32} className="text-purple-500 mx-auto mb-4" />,
      title: "Podcast-Native AI",
      description: "Our AI is trained specifically on podcasts to deliver you the best possible results",
    },
    {
      icon: <LucideBot size={32} className="text-purple-500 mx-auto mb-4" />,
      title: "SmartHook Finder",
      description: "Automatically finds attention-grabbing openings, ensuring viewers stay locked in",
    },
    {
      icon: <LucideCrop size={32} className="text-purple-500 mx-auto mb-4" />,
      title: "Perfect Vertical Framing",
      description: "Smart tracking keeps the active speaker in frame — no manual cropping needed",
    },
    {
      icon: <LucideSubtitles size={32} className="text-purple-500 mx-auto mb-4" />,
      title: "Precision-Perfect Subtitles",
      description: "Auto-generated captions with 99% accuracy and customizable styles that match your brand identity",
    },
    {
      icon: <LucideFileVideo2 size={32} className="text-purple-500 mx-auto mb-4" />,
      title: "Easy Episode Import",
      description: "Effortlessly upload your episode file or paste a YouTube link—we handle the rest",
    },
    {
      icon: <LucideListVideo size={32} className="text-purple-500 mx-auto mb-4" />,
      title: "Intuitive Timeline Editor",
      description: "Fine-tune your AI-generated clips with our simple and refined interface",
    }
  ];



  const handleScrollToSection = (sectionId: Links) => {

    const section = document.getElementById(stripHash(sectionId));
    if (section) {
      section.scrollIntoView({ behavior: "smooth" });
    }
  }


  return (
    <>
      <section className="px-6 py-20 flex flex-col md:flex-row items-center max-w-7xl mx-auto">
        <div className="flex-1">
          <h1 className="text-2xl md:text-5xl font-black mb-4 font-">
            <span className="hover-vibrate inline-block transition-all">Podcast-Native AI</span> Turns Every Episode into 10+ Ready-to-Post Shorts on Autopilot</h1>
          <p className="mb-6 text-gray-300">Boostcast’s AI was trained exclusively on podcast episodes. It recognizes conversation patterns, identifies natural hooks, and crafts shorts that actually convert. Focus on creating what matters while we handle the rest.</p>
          <div className="flex gap-4">
            <a
              href="https://create.boostcast.pro"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-purple-600 text-white px-4 py-3 rounded font-semibold hover:bg-purple-700 flex flex-col items-center leading-tight text-sm"
            >
              <span className="text-base font-semibold">Start FREE trial</span>
              <span className="text-xs opacity-100">(no credit card required)</span>
            </a>
            <button onClick={() => handleScrollToSection(Links.Pricing)} className="border border-white px-6 py-3 rounded font-semibold hover:bg-white hover:text-black">See pricing</button>
          </div>
        </div>
        <div className="flex-1 mt-10 md:mt-0 flex justify-center">
          <Image width={1000} height={1000} src="/images/hero-with-text.png" alt="Video preview" className="rounded-xl max-w-xs md:max-w-xl" />
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="bg-zinc-900 py-16 px-6">
        <div className="text-center max-w-6xl mx-auto">
          <h2 className="text-2xl md:text-5xl font-black mb-10">Trusted by 7000+ podcasters and marketing experts who reclaimed their creative time</h2>
          <div className="grid md:grid-cols-4 gap-4 text-sm text-left">
            {cards.map((card, idx) => (
              <TestimonialCard
                key={idx}
                image={card.image}
                name={card.name}
                title={card.title}
                quote={card.quote}
              />
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="information" className="py-20 px-6">
        <h2 className="text-5xl font-black text-center mb-16">How The Platform Works</h2>
        <div className="space-y-16 max-w-6xl mx-auto">
          {[
            {
              image: "/images/upload-cropped.gif",
              title: "Step 1",
              description: "Upload your podcast episode or paste a YouTube link.",
            },
            {
              image: "/images/view-clips-cropped.gif",
              title: "Step 2",
              description: "Let our podcast-native AI analyze and find the best moments for shorts.",
            },
            {
              image: "/images/step-3-download.png",
              title: "Step 3",
              description: "Download high-quality, Ready-to-Post clips.",
            },
          ].map((step, idx) => (
            <div
              key={idx}
              className={`flex flex-col md:flex-row items-center gap-10 ${idx % 2 === 1 ? "md:flex-row-reverse" : ""
                }`}
            >
              <img
                src={step.image}
                alt={step.title}
                className="w-full md:w-1/2 rounded-xl"
              />
              <div className="text-center md:text-left md:w-1/2">
                <h3 className="text-2xl font-semibold mb-2">{step.title}</h3>
                <p className="text-gray-400">{step.description}</p>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Features */}
      <section id="features" className="bg-zinc-900 py-20 px-6">
        <h2 className="text-3xl font-black text-center mb-12">AI That Thinks Like An Editor</h2>
        <div className="grid md:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {features.map((feature, idx) => (
            <FeatureCard key={idx} icon={feature.icon} title={feature.title} description={feature.description} />
          ))}
        </div>
      </section>

      {/* Pricing */}
      <section id="pricing" className="py-20 px-6 text-center">
        <h2 className="text-3xl font-black mb-12">Our Simple Pricing</h2>
        <div className="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
          {prices.map((card, idx) => (
            <PricingCard
              key={idx}
              title={card.title}
              price={card.price}
              period={card.period}
              currency="$"
              features={card.features}
              cta={card.cta}
              ctaUrl={card.ctaUrl}
              subtext={card.subtext}
              highlighted={card.highlighted}
            />
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-zinc-900 py-16 px-6 text-center">
        <h2 className="text-2xl font-black mb-4">Join our Discord</h2>
        <p className="text-gray-400 mb-6 max-w-xl mx-auto">Connect with fellow content creators in our Discord. Request features, share success stories, and get inspired by what others are creating with Boostcast.</p>
        <a
          href="https://discord.gg/BXNyyzAvHP"
          target="_blank"
          rel="noopener noreferrer"
          className="inline-block"
        >
          <img
            src="/images/discord-logo.png"
            alt="Join our Discord"
            className="mx-auto w-12 hover:opacity-80 transition-opacity"
          />
        </a>
      </section>
    </>

  );
}
