@import "tailwindcss";
@tailwind base;
@tailwind components;
@tailwind utilities;

.hover-vibrate:hover {
  animation: vibrate 0.3s ease-in-out;
}

@keyframes vibrate {
  0%,
  100% {
    transform: translateX(0);
  }
  20% {
    transform: translateX(-2px);
  }
  40% {
    transform: translateX(2px);
  }
  60% {
    transform: translateX(-2px);
  }
  80% {
    transform: translateX(2px);
  }
}

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Custom purple color variables */
  --purple-primary: #7852FF;
  --purple-400: #7852FF;
  --purple-500: #7852FF;
  --purple-600: #7852FF;
  --purple-700: #6B46E6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-poppins);
  --font-mono: var(--font-geist-mono);

  /* Custom purple colors for Tailwind */
  --color-purple-400: var(--purple-400);
  --color-purple-500: var(--purple-500);
  --color-purple-600: var(--purple-600);
  --color-purple-700: var(--purple-700);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;

    /* Purple colors remain the same in dark mode */
    --purple-primary: #7852FF;
    --purple-400: #7852FF;
    --purple-500: #7852FF;
    --purple-600: #7852FF;
    --purple-700: #6B46E6;
  }
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-poppins), Arial, Helvetica, sans-serif;
}

button {
  cursor: pointer;
}
