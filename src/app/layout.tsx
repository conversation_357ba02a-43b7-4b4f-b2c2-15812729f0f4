import type { Metada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";
import { MenuItemsProps } from "@/components/Menu/menu.types";
import { Links } from "@/utils/types";
import Menu from "@/components/Menu";
import Footer from "@/components/Footer";
import AnalyticsProvider from "@/components/Analytics/AnalyticsProvider";
import FacebookPixelNoscript from "@/components/Analytics/FacebookPixelNoscript";

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  weight: ["400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "boostcast",
  description: "Boost your productivity with Boostcast, the AI-powered podcast app that helps you edit faster and retain more.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  const menuItems: MenuItemsProps[] = [
    { label: "Information", href: Links.Information },
    { label: "Features", href: Links.Features },
    { label: "Pricing", href: Links.Pricing },
    { label: "Testimonials", href: Links.Testimonials },
    // { label: "Blog", href: Links.Blog },
  ];

  return (
    <html lang="en">
      <body
        className={`${poppins.variable} antialiased`}
      >
        <FacebookPixelNoscript />
        <AnalyticsProvider>
          <main className="bg-black text-white font-sans">
            <Menu items={menuItems} />
            {children}
            <Footer />
          </main>
        </AnalyticsProvider>
      </body>
    </html>
  );
}
