// TypeScript declarations for Facebook Pixel (Meta Pixel)

declare global {
  interface Window {
    fbq: FacebookPixel;
    _fbq: FacebookPixel;
  }
}

interface FacebookPixel {
  (command: 'init', pixelId: string, options?: object): void;
  (command: 'track', eventName: string, parameters?: object): void;
  (command: 'trackCustom', eventName: string, parameters?: object): void;
  (command: 'consent', action: string): void;
  callMethod?: (...args: any[]) => void;
  queue?: any[];
  push?: (args: any[]) => void;
  loaded?: boolean;
  version?: string;
}

export {};
