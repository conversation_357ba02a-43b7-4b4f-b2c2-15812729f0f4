import <PERSON>act<PERSON> from 'react-ga4';
import Tag<PERSON>anager from 'react-gtm-module';
import { hotjar } from 'react-hotjar';
import { clarity } from 'react-microsoft-clarity';

// Analytics configuration
interface AnalyticsConfig {
  googleAnalyticsId?: string;
  tagManagerId?: string;
  hotjarId?: string;
  clarityId?: string;
  facebookPixelId?: string;
}

// Get analytics configuration from environment variables
const getAnalyticsConfig = (): AnalyticsConfig => {
  return {
    googleAnalyticsId: process.env.NEXT_PUBLIC_ANALYTICS_ID,
    tagManagerId: process.env.NEXT_PUBLIC_TAG_MANAGER_ID,
    hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID,
    clarityId: process.env.NEXT_PUBLIC_CLARITY_ID,
    facebookPixelId: process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID,
  };
};

// Check if we're in a browser environment
const isBrowser = (): boolean => {
  return typeof window !== 'undefined';
};

let analyticsInitialized = false;

// Initialize Facebook Pixel (Meta Pixel) with native JavaScript
const initializeFacebookPixel = (pixelId: string): void => {
  try {
    console.log('Attempting to initialize Facebook Pixel with ID:', pixelId);

    // Check if fbq is already loaded and initialized
    if (window.fbq && window.fbq.loaded) {
      console.log('Facebook Pixel already initialized');
      return;
    }

    // Initialize fbq function if it doesn't exist
    if (!window.fbq) {
      console.log('Creating fbq function...');
      const fbq: any = function() {
        fbq.callMethod ? fbq.callMethod.apply(fbq, arguments) : fbq.queue.push(arguments);
      };
      fbq.push = fbq;
      fbq.loaded = false; // Set to false initially, will be set to true when script loads
      fbq.version = '2.0';
      fbq.queue = [];
      window.fbq = fbq;
      window._fbq = fbq;
    }

    // Load the Facebook Pixel script
    console.log('Loading Facebook Pixel script...');
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://connect.facebook.net/en_US/fbevents.js';

    // Add onload handler to ensure script is loaded before initializing
    script.onload = () => {
      console.log('Facebook Pixel script loaded successfully');
      try {
        // Initialize the pixel with the provided ID
        window.fbq('init', pixelId);
        console.log('Facebook Pixel initialized with ID:', pixelId);

        // Track initial PageView
        window.fbq('track', 'PageView');
        console.log('Facebook Pixel PageView tracked');
      } catch (initError) {
        console.error('Error during Facebook Pixel initialization:', initError);
      }
    };

    script.onerror = () => {
      console.error('Failed to load Facebook Pixel script');
    };

    const firstScript = document.getElementsByTagName('script')[0];
    if (firstScript && firstScript.parentNode) {
      firstScript.parentNode.insertBefore(script, firstScript);
    } else {
      // Fallback: append to head
      document.head.appendChild(script);
    }

    console.log('Facebook Pixel script injection completed');
  } catch (error) {
    console.error('Failed to initialize Facebook Pixel:', error);
  }
};

// Initialize all analytics services
export const initializeAnalytics = (): void => {
  console.log('initializeAnalytics called');
  console.log('isBrowser():', isBrowser());
  console.log('analyticsInitialized:', analyticsInitialized);

  if (!isBrowser() || analyticsInitialized) return;

  const config = getAnalyticsConfig();
  console.log('Analytics config:', config);

  // Initialize Google Analytics 4
  if (config.googleAnalyticsId) {
    try {
      ReactGA.initialize(config.googleAnalyticsId, {
        testMode: process.env.NODE_ENV === 'development',
      });
      console.log('Google Analytics initialized');
    } catch (error) {
      console.error('Failed to initialize Google Analytics:', error);
    }
  }

  // Initialize Google Tag Manager
  if (config.tagManagerId) {
    try {
      TagManager.initialize({
        gtmId: config.tagManagerId,
      });
      console.log('Google Tag Manager initialized');
    } catch (error) {
      console.error('Failed to initialize Google Tag Manager:', error);
    }
  }

  // Initialize Hotjar
  if (config.hotjarId) {
    try {
      const hjid = parseInt(config.hotjarId, 10);
      if (!isNaN(hjid)) {
        hotjar.initialize({ id: hjid, sv: 6 });
        console.log('Hotjar initialized');
      }
    } catch (error) {
      console.error('Failed to initialize Hotjar:', error);
    }
  }

  // Initialize Microsoft Clarity
  if (config.clarityId) {
    try {
      clarity.init(config.clarityId);
      console.log('Microsoft Clarity initialized');
    } catch (error) {
      console.error('Failed to initialize Microsoft Clarity:', error);
    }
  }

  // Initialize Facebook Pixel
  if (config.facebookPixelId) {
    console.log('Initializing Facebook Pixel with ID:', config.facebookPixelId);
    initializeFacebookPixel(config.facebookPixelId);
  } else {
    console.log('No Facebook Pixel ID found in environment variables');
  }

  analyticsInitialized = true;
  console.log('Analytics initialization completed');
};



// Check if analytics should be enabled (has at least one tracking ID)
export const isAnalyticsEnabled = (): boolean => {
  const config = getAnalyticsConfig();
  return !!(
    config.googleAnalyticsId ||
    config.tagManagerId ||
    config.hotjarId ||
    config.clarityId ||
    config.facebookPixelId
  );
};

// Facebook Pixel tracking functions
export const trackFacebookPixelEvent = (eventName: string, parameters?: object): void => {
  if (!isBrowser() || !window.fbq) return;

  try {
    window.fbq('track', eventName, parameters);
  } catch (error) {
    console.error('Failed to track Facebook Pixel event:', error);
  }
};

export const trackFacebookPixelCustomEvent = (eventName: string, parameters?: object): void => {
  if (!isBrowser() || !window.fbq) return;

  try {
    window.fbq('trackCustom', eventName, parameters);
  } catch (error) {
    console.error('Failed to track Facebook Pixel custom event:', error);
  }
};

// Track page view for Facebook Pixel (useful for SPA navigation)
export const trackFacebookPixelPageView = (): void => {
  if (!isBrowser() || !window.fbq) return;

  try {
    window.fbq('track', 'PageView');
  } catch (error) {
    console.error('Failed to track Facebook Pixel PageView:', error);
  }
};
