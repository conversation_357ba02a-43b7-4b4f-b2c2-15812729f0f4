// Debug utility for analytics troubleshooting
export const debugAnalytics = () => {
  console.log('=== ANALYTICS DEBUG INFO ===');
  
  // Check environment
  console.log('Environment:', process.env.NODE_ENV);
  console.log('Browser check:', typeof window !== 'undefined');
  
  // Check environment variables
  console.log('Environment Variables:');
  console.log('- NEXT_PUBLIC_FACEBOOK_PIXEL_ID:', process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID);
  console.log('- NEXT_PUBLIC_ANALYTICS_ID:', process.env.NEXT_PUBLIC_ANALYTICS_ID);
  console.log('- NEXT_PUBLIC_TAG_MANAGER_ID:', process.env.NEXT_PUBLIC_TAG_MANAGER_ID);
  console.log('- NEXT_PUBLIC_HOTJAR_ID:', process.env.NEXT_PUBLIC_HOTJAR_ID);
  console.log('- NEXT_PUBLIC_CLARITY_ID:', process.env.NEXT_PUBLIC_CLARITY_ID);
  
  if (typeof window !== 'undefined') {
    // Check Facebook Pixel
    console.log('Facebook Pixel Status:');
    console.log('- window.fbq exists:', !!window.fbq);
    console.log('- window._fbq exists:', !!window._fbq);
    
    if (window.fbq) {
      console.log('- fbq.loaded:', window.fbq.loaded);
      console.log('- fbq.version:', window.fbq.version);
      console.log('- fbq.queue length:', window.fbq.queue?.length || 0);
    }
    
    // Check for Facebook Pixel script
    const fbScripts = Array.from(document.querySelectorAll('script')).filter(
      script => script.src.includes('fbevents.js')
    );
    console.log('- Facebook Pixel scripts found:', fbScripts.length);
    
    // Check for noscript fallback
    const noscriptTags = Array.from(document.querySelectorAll('noscript')).filter(
      noscript => noscript.innerHTML.includes('facebook.com/tr')
    );
    console.log('- Noscript fallbacks found:', noscriptTags.length);
    
    // Network requests check
    console.log('To check network requests:');
    console.log('1. Open DevTools > Network tab');
    console.log('2. Filter by "fbevents" or "facebook.com"');
    console.log('3. Reload the page');
    console.log('4. Look for requests to connect.facebook.net and www.facebook.com');
  }
  
  console.log('=== END DEBUG INFO ===');
};

// Test Facebook Pixel manually
export const testFacebookPixel = () => {
  if (typeof window === 'undefined') {
    console.log('Cannot test Facebook Pixel - not in browser environment');
    return;
  }
  
  if (!window.fbq) {
    console.log('Facebook Pixel not loaded - fbq function not found');
    return;
  }
  
  try {
    console.log('Testing Facebook Pixel...');
    window.fbq('track', 'PageView');
    console.log('✅ Facebook Pixel PageView test successful');
    
    // Test custom event
    window.fbq('trackCustom', 'DebugTest', { source: 'manual_test' });
    console.log('✅ Facebook Pixel custom event test successful');
  } catch (error) {
    console.error('❌ Facebook Pixel test failed:', error);
  }
};

// Add to window for easy access in browser console
if (typeof window !== 'undefined') {
  (window as any).debugAnalytics = debugAnalytics;
  (window as any).testFacebookPixel = testFacebookPixel;
}
